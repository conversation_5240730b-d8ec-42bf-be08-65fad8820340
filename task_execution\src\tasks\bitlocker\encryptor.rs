use crate::tasks::bitlocker::encryption_volume_status::EncryptionVolumeStatus;
use anyhow::Result;
use database::models::{BitlockerPolicy, DriveEncryptionType, EncryptionMethod, TpmProtectionMode};
use logger::debug;
use windows::{
    core::PCWSTR,
    Wdk::System::SystemServices::RtlGetVersion,
    Win32::{
        Storage::FileSystem::{GetDriveTypeW, GetLogicalDriveStringsW},
        System::{
            SystemInformation::OSVERSIONINFOW,
            TpmBaseServices::Tbsi_Is_Tpm_Present,
            WindowsProgramming::{DRIVE_FIXED, DRIVE_REMOVABLE},
        },
    },
};

fn make_bstr_variant(s: &str) -> VARIANT {
    unsafe {
        let mut v = VARIANT::default();
        v.Anonymous.Anonymous.vt = VT_BSTR.0 as u16;
        v.Anonymous.Anonymous.Anonymous.bstrVal = SysAllocString(&HSTRING::from(s));
        v
    }
}

#[derive(Debug, Default)]
struct DiskDrives {
    os_drive: String,
    fixed_drives: Vec<String>,
    removable_drives: Vec<String>,
}

impl DiskDrives {
    pub fn detect() -> Result<Self> {
        let mut buffer = [0u16; 256];
        unsafe {
            GetLogicalDriveStringsW(Some(&mut buffer));
        }

        let drives: Vec<String> = buffer
            .split(|c| *c == 0)
            .filter(|s| !s.is_empty())
            .map(String::from_utf16_lossy)
            .collect();

        let mut result = Self::default();

        for drive in drives {
            let drive_chars: Vec<u16> = drive.encode_utf16().chain([0]).collect();
            let drive_type = unsafe { GetDriveTypeW(PCWSTR(drive_chars.as_ptr())) };

            match drive_type {
                DRIVE_FIXED => result
                    .fixed_drives
                    .push(drive.trim_end_matches('\\').to_string()),
                DRIVE_REMOVABLE => result
                    .removable_drives
                    .push(drive.trim_end_matches('\\').to_string()),
                _ => {}
            }

            // Detect OS drive
            if Self::is_os_drive(&drive) {
                result.os_drive = drive.trim_end_matches('\\').to_string();
            }
        }

        debug!("Detected drives: {:?}", result);

        Ok(result)
    }

    fn is_os_drive(drive: &str) -> bool {
        let system_root = std::env::var("SystemRoot").unwrap_or("C:\\Windows".to_string());
        system_root.starts_with(drive)
    }
}

pub struct Encryptor<'a> {
    policy: &'a BitlockerPolicy,
    has_tpm: bool,
    volume_status: Vec<EncryptionVolumeStatus>,
    os_version: String,
    disks: DiskDrives,
    pin: Option<String>,
    password: Option<String>,
}

impl<'a> Encryptor<'a> {
    pub fn new(
        policy: &'a BitlockerPolicy,
        pin: Option<String>,
        password: Option<String>,
    ) -> Result<Self> {
        Ok(Self {
            policy,
            has_tpm: Self::detect_tpm()?,
            os_version: Self::os_version(),
            disks: DiskDrives::detect()?,
            volume_status: EncryptionVolumeStatus::get_all()?,
            pin,
            password,
        })
    }

    fn detect_tpm() -> Result<bool> {
        let result = unsafe { Tbsi_Is_Tpm_Present() };
        debug!("TPM present: {}", result.as_bool());
        Ok(result.as_bool())
    }

    fn os_version() -> String {
        unsafe {
            let mut info = OSVERSIONINFOW {
                dwOSVersionInfoSize: std::mem::size_of::<OSVERSIONINFOW>() as u32,
                ..Default::default()
            };

            let status = RtlGetVersion(&mut info);

            let version = if status.is_ok() {
                match (info.dwMajorVersion, info.dwMinorVersion) {
                    (10, 0) => {
                        if info.dwBuildNumber >= 22000 {
                            "Win11"
                        } else {
                            "Win10"
                        }
                    }
                    (6, 3) => "Win8.1",
                    (6, 2) => "Win8",
                    (6, 1) => "Win7",
                    _ => "Unknown Windows version",
                }
            } else {
                "Unknown Windows version"
            }
            .to_string();

            debug!("OS version: {}", version);

            version
        }
    }

    pub fn execute(&self) -> Result<()> {
        let encryption_method = match self.os_version.as_str() {
            "win7" => &self.policy.encryption_method_win7,
            "win8" | "win8.1" => &self.policy.encryption_method_win8,
            "win10" | "win11" => &self.policy.encryption_method_win10,
            _ => &self.policy.encryption_method_win10,
        };

        // 4. OS DRIVE encryption
        if self.policy.encrypt_os_drive {
            self.encrypt_drive(
                &self.disks.os_drive,
                true,
                &encryption_method,
                &self.policy.os_drive_encryption_type,
            )?;
        }

        // 5. Fixed drives
        if self.policy.encrypt_fixed_drives {
            for disk in self.disks.fixed_drives.iter() {
                if &self.disks.os_drive == disk {
                    continue;
                }
                self.encrypt_drive(
                    &disk,
                    false,
                    &encryption_method,
                    &self.policy.fixed_drive_encryption_type,
                )?;
            }
        }

        // 6. Removable drives
        if self.policy.encrypt_removable_drives {
            for disk in self.disks.removable_drives.iter() {
                self.encrypt_drive(
                    &disk,
                    &encryption_method,
                    &self.policy.removable_drive_encryption_type,
                )?;
            }
        }

        Ok(())
    }

    fn encrypt_drive(
        &self,
        drive: &str,
        is_os_drive: bool,
        encryption_method: &EncryptionMethod,
        encryption_type: &DriveEncryptionType,
    ) -> Result<()> {
        let encryption_cmd = encryption_method.to_command();

        if is_os_drive {
            // TPM + PIN logic
            if self.has_tpm == false && self.policy.allow_without_tpm == false {
                anyhow::bail!("TPM is not available and policy forbids encryption without TPM");
            }
            self.add_protector(drive)?;
        }

        let encryption_command = format!(
            "manage-bde.exe -on {} -EncryptionMethod {}{}",
            drive,
            encryption_cmd,
            if encryption_type == &DriveEncryptionType::UsedSpaceOnly {
                " -UsedSpaceOnly"
            } else {
                ""
            }
        );

        debug!("Built command to encrypt drive: {}", encryption_command);

        Ok(())
    }

    fn add_protector(&self, drive: &str) -> Result<()> {
        use windows::{
            core::*, Win32::System::Com::*, Win32::System::Variant::*, Win32::System::*,
        };

        unsafe {
            CoInitializeEx(None, COINIT_MULTITHREADED)?;

            CoInitializeSecurity(
                None,
                -1,
                None,
                None,
                RPC_C_AUTHN_LEVEL_DEFAULT,
                RPC_C_IMP_LEVEL_IMPERSONATE,
                None,
                EOAC_NONE,
                None,
            )?;

            // Create WMI locator
            let locator: IWbemLocator = CoCreateInstance(&WbemLocator, None, CLSCTX_INPROC_SERVER)?;

            // Connect to BitLocker namespace
            let services: IWbemServices = locator.ConnectServer(
                &BSTR::from("ROOT\\CIMV2\\Security\\MicrosoftVolumeEncryption"),
                None,
                None,
                None,
                0,
                None,
                None,
            )?;

            // Impersonation
            CoSetProxyBlanket(
                &services,
                RPC_C_AUTHN_WINNT,
                RPC_C_AUTHZ_NONE,
                None,
                RPC_C_AUTHN_LEVEL_CALL,
                RPC_C_IMP_LEVEL_IMPERSONATE,
                None,
                EOAC_NONE,
            )?;

            // Query C: volume
            let mut enumerator = None;
            services.ExecQuery(
                &BSTR::from("WQL"),
                &BSTR::from("SELECT * FROM Win32_EncryptableVolume WHERE DriveLetter='C:'"),
                WBEM_FLAG_FORWARD_ONLY | WBEM_FLAG_RETURN_IMMEDIATELY,
                None,
                &mut enumerator,
            )?;

            let mut obj = None;
            enumerator
                .unwrap()
                .Next(WBEM_INFINITE, &mut obj, std::ptr::null_mut())?;

            let volume: IWbemClassObject = obj.unwrap();

            // Get input param class
            let mut in_class = None;
            volume.GetMethod(
                &BSTR::from("ProtectKeyWithPassphrase"),
                0,
                &mut in_class,
                std::ptr::null_mut(),
            )?;

            // Spawn instance
            let mut in_inst = None;
            in_class.unwrap().SpawnInstance(0, &mut in_inst)?;

            // Set password
            let variant_pass = make_bstr_variant("MyPass123!");
            in_inst
                .as_ref()
                .unwrap()
                .Put(&BSTR::from("Passphrase"), &variant_pass, 0)?;

            // Call the method
            let mut out_params = None;
            services.ExecMethod(
                &BSTR::from("Win32_EncryptableVolume.DriveLetter=\"C:\""),
                &BSTR::from("ProtectKeyWithPassphrase"),
                0,
                None,
                &in_inst.unwrap(),
                &mut out_params,
                None,
            )?;

            // Return code
            let mut ret = VARIANT::default();
            out_params.unwrap().Get(
                &BSTR::from("ReturnValue"),
                0,
                &mut ret,
                std::ptr::null_mut(),
                std::ptr::null_mut(),
            )?;

            println!("ReturnValue = {}", ret.ulVal());

            CoUninitialize();
        }

        Ok(())

        // match self.policy.tpm_protection_mode {
        //     TpmProtectionMode::TpmOnly => {
        //         if self.has_tpm == false {
        //             if self.password.is_none() {
        //                 anyhow::bail!("Password is required for TPM-less protection mode");
        //             } else {
        //                 debug!("Password is available for TPM-less protection mode");
        //             }
        //         } else {
        //             debug!("TPM is available for TPM-only protection mode");
        //         }
        //     }
        //     TpmProtectionMode::TpmAndPin => {
        //         if self.pin.is_none() {
        //             anyhow::bail!("PIN is required for TPM and PIN protection mode");
        //         }
        //         debug!("PIN is available for TPM and PIN protection mode");
        //     }
        // }
        // Ok(())
    }
}
